package main

import (
	"git.woa.com/bkdevops/atomDemoGolang/hello"
	"git.woa.com/bkdevops/atomDemoGolang/translation"
	"git.woa.com/bkdevops/golang-atom-sdk/api"
	"git.woa.com/bkdevops/golang-atom-sdk/log"
)

//go:generate i18ngenerator i18n ./translation/translation.go

func main() {
	log.Info("atom-demo-glang starts")
	defer func() {
		if err := recover(); err != nil {
			log.Error("panic: ", err)
			api.FinishBuild(api.StatusError, "panic occurs")
		}
	}()

	api.InitI18n(translation.Translations, api.GetRuntimeLanguage())
	msg, err := api.Localize("input.desc.label")
	if err != nil {
		log.Error(err)
	}
	log.Info(msg)

	hello.HelloBuild()
}
