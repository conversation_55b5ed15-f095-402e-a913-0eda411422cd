// Code generated by "i18ngenerator"; DO NOT EDIT.

package translation

// Translations
var Translations map[string][][]string = make(map[string][][]string)

func init() {
	Translations["en-US"] = [][]string{
		{
			"100001",
			"input param [{0}] invitated",
		},
		{
			"input.desc.label",
			"desc",
		},
	}
	Translations["zh-CN"] = [][]string{
		{
			"100001",
			"输入参数[{0}]非法",
		},
		{
			"input.desc.label",
			"描述",
		},
	}
}
