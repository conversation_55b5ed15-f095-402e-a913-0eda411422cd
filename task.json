{"atomCode": "token_replace", "execution": {"language": "golang", "os": [{"osName": "linux", "osArch": "amd64", "target": "./linux-amd64-app", "demands": ["echo hello"], "defaultFlag": true}, {"osName": "windows", "osArch": "386", "target": "windows-386-app", "demands": [], "defaultFlag": true}]}, "input": {"tips": {"label": "", "type": "tips", "tipStr": "本插件用于在流水线中替换在 git 仓库中文件内的一些敏感 token 数据，比如 trpc 配置文件中的 rainbow 的用户 token"}, "file_names": {"label": "需处理文件，多个文件使用英文逗号分隔", "placeholder": "trpc_go.yaml,trpc_dev.yaml", "type": "vuex-input", "desc": "", "required": true, "disabled": false, "hidden": false, "isSensitive": false}, "handle_path": {"label": "处理路径", "desc": "默认处理 workspace 路径", "type": "vuex-input", "default": ""}, "enable_recursion": {"text": "是否遍历处理子路径", "default": true, "type": "atom-checkbox", "desc": "是否遍历处理子路径中的文件，否则只处理当前目录下的文件", "required": false, "disabled": false, "hidden": false, "isSensitive": false}, "enable_bak": {"text": "是否修改前备份文件", "default": false, "type": "atom-checkbox", "desc": "修改之前是否备份被修改的文件，备份文件会备份在相同目录中，增加 bak 后缀，但是是否打包到制品中，由流水线中打包环节决定", "required": false, "disabled": false, "hidden": false, "isSensitive": false}, "params": {"label": "替换列表", "type": "dynamic-parameter-simple", "parameters": [{"rowAttributes": [{"id": "key", "label": "变量名", "type": "input", "placeholder": "${ci.rainbow.user_id}", "desc": "会被替换的变量名称，建议格式 ${ci.xxx.yyy}, 如：${ci.rainbow.user_id}", "default": "${ci.rainbow.user_id}"}, {"id": "value", "label": "替换值", "type": "input", "placeholder": "", "desc": "用于替换的内容，如果是蓝盾，可以直接写在表单中，如果是 streamci，建议使用凭证管理这里的内容，避免敏感密钥出现在流水线 yaml 中", "default": ""}]}], "desc": ""}}, "output": {"modified_file_names": {"description": "被修改的文件列表", "type": "string", "isSensitive": false}, "modified_files": {"description": "被修改的文件", "type": "artifact", "isSensitive": false}}}