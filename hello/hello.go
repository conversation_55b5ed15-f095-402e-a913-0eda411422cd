// Package hello 实例
package hello

import (
	"fmt"
	"io/ioutil"
	"os"
	"time"

	"git.woa.com/bkdevops/golang-atom-sdk/api"
	"git.woa.com/bkdevops/golang-atom-sdk/log"
)

// greetingParam 自定义参数
type greetingParam struct {
	UserName string `json:"userName"`
	Greeting string `json:"greeting"`
}

// greetingParam
func (a *greetingParam) String() string {
	return fmt.Sprintf("userName: %v, greeting: %v", a.UserName, a.Greeting)
}

// HelloBuild 插件代码
func HelloBuild() {
	// 获取单个输入参数
	userName := api.GetInputParam("userName")
	log.Info("userName: ", userName)

	// 打屏
	log.Info("\nBuildInfo:")
	log.Info("Project Name:     ", api.GetProjectDisplayName())
	log.Info("Pipeline Id:      ", api.GetPipelineId())
	log.Info("Pipeline Name:    ", api.GetPipelineName())
	log.Info("Pipeline Version: ", api.GetPipelineVersion())
	log.Info("Build Id:         ", api.GetPipelineBuildId())
	log.Info("Build Num:        ", api.GetPipelineBuildNumber())
	log.Info("Start Type:       ", api.GetPipelineStartType())
	log.Info("Start UserId:     ", api.GetPipelineStartUserId())
	log.Info("Start UserName:   ", api.GetPipelineStartUserName())
	log.Info("Start Time:       ", api.GetPipelineStartTimeMills())
	log.Info("Workspace:        ", api.GetWorkspace())
	log.Info("PostActionParam:  ", api.GetPostActionParam())
	log.Info("TaskId:           ", api.GetTaskId())
	log.Info("StepId:           ", api.GetStepId())

	// postActionParam 和 task.json 配置中的 postEntryParam 的值对齐
	if api.GetPostActionParam() == "postEntry" {
		// 后置动作
		return
	}

	// 输入参数解析到对象
	paramData := new(greetingParam)
	api.LoadInputParam(paramData)
	log.Info(fmt.Sprintf("\n%v，%v\n", paramData.Greeting, paramData.UserName))

	// 获取插件的敏感信息
	log.Info(fmt.Sprintf("Sensitive: %v", api.GetSensitiveConfParam("your_sensitive_key")))

	// 业务逻辑
	log.Info("start build")
	build()
	time.Sleep(2 * time.Second)

	// 输出
	// 字符串输出
	strData := api.NewStringData("test")
	api.AddOutputData("strData_01", strData)

	// 文件归档输出
	str := "这是要写入文件的字符串"
	filename := "result.dat"
	err := ioutil.WriteFile(filename, []byte(str), 0644)
	if err != nil {
		fmt.Println("写入文件失败：", err)
		return
	}

	// 文件归档输出到流水线仓库
	artifactData1 := api.NewArtifactData()
	artifactData1.AddArtifact(filename)
	api.AddOutputData("artifactData_01", artifactData1)

	// 文件归档输出到自定义仓库
	artifactData2 := api.NewArtifactData()
	artifactData2.AddArtifactToCustomRepo(filename, "./")
	api.AddOutputData("artifactData_02", artifactData2)

	// 报告输出
	reportData := api.NewReportData("label_01",
		api.GetWorkspace()+"/report", "report.htm")
	api.AddOutputData("report_01", reportData)

	// 设置质量红线输出
	api.SetAtomOutputType("quality")
	api.AddQualityData("indicator_int", api.NewQualityData("1"))
	api.AddQualityData("indicator_float", api.NewQualityData("2.11"))
	api.AddQualityData("indicator_boolean", api.NewQualityData("false"))
	log.Info(api.GetQualityData("indicator_int"))

	// 获取蓝盾上下文（包括环境变量）
	log.Info(api.GetVariableByName("ci.pipeline_id"))

	api.WriteOutput()
	log.Info("build done")

	// demo默认异常，自定义的错误码做异常退出，自定义逻辑实现后请改为FinishBuild
	//if err != nil {
	myErrorCode := 12345
	api.FinishBuildWithError(api.StatusError, "panic occurs with code",
		myErrorCode, api.UserError)
	//}
}

func build() {
	log.Info("write result.dat")
	ioutil.WriteFile(api.GetWorkspace()+"/result.dat", []byte("content"), 0644)
	log.Info("write report.htm")
	os.Mkdir(api.GetWorkspace()+"/report", os.ModePerm)
	ioutil.WriteFile(
		api.GetWorkspace()+"/report/report.htm",
		[]byte("<html><head><title>Report</title></head><body><H1>This is a "+
			"Report</H1></body></html>"),
		0644)
}
